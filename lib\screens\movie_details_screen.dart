import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/tmdb_service.dart';
import '../models/movie.dart';
import '../models/cast.dart';
import '../widgets/cast_list.dart';

class MovieDetailsScreen extends StatefulWidget {
  final String movieId;

  const MovieDetailsScreen({
    super.key,
    required this.movieId,
  });

  @override
  _MovieDetailsScreenState createState() => _MovieDetailsScreenState();
}

class _MovieDetailsScreenState extends State<MovieDetailsScreen> {
  final TMDBService _tmdbService = TMDBService();
  MovieDetails? movieDetails;
  Credits? credits;
  List<Video> videos = [];
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    _loadMovieDetails();
  }

  Future<void> _loadMovieDetails() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      final details = await _tmdbService.getMovieDetails(widget.movieId);
      final movieCredits = await _tmdbService.getMovieCredits(widget.movieId);
      final movieVideos = await _tmdbService.getMovieVideos(widget.movieId);

      setState(() {
        movieDetails = details;
        credits = movieCredits;
        videos = movieVideos;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : error != null
              ? _buildErrorWidget()
              : _buildMovieDetails(),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            'Failed to load movie details',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadMovieDetails,
            child: Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildMovieDetails() {
    if (movieDetails == null) return SizedBox.shrink();

    return CustomScrollView(
      slivers: [
        // App Bar with Backdrop
        SliverAppBar(
          expandedHeight: 300,
          pinned: true,
          backgroundColor: Colors.black,
          leading: Container(
            margin: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.5),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: Icon(Icons.arrow_back_ios, color: Colors.white),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          actions: [
            Container(
              margin: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: Icon(Icons.favorite_border, color: Colors.white),
                onPressed: () {
                  // TODO: Add to favorites
                },
              ),
            ),
          ],
          flexibleSpace: FlexibleSpaceBar(
            background: Stack(
              fit: StackFit.expand,
              children: [
                CachedNetworkImage(
                  imageUrl: TMDBService.getImageUrl(
                    movieDetails!.backdropPath ?? movieDetails!.posterPath,
                    isBackdrop: true,
                  ),
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey[300],
                    child: Center(child: CircularProgressIndicator()),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey[300],
                    child: Icon(Icons.movie, size: 64),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.7),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // Movie Content
        SliverToBoxAdapter(
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Movie Title and Basic Info
                _buildMovieHeader(),
                SizedBox(height: 16),

                // Action Buttons
                _buildActionButtons(),
                SizedBox(height: 24),

                // Overview
                _buildOverview(),
                SizedBox(height: 24),

                // Cast
                if (credits != null && credits!.cast.isNotEmpty)
                  CastList(cast: credits!.cast),
                SizedBox(height: 24),

                // Movie Details
                _buildMovieInfo(),
                SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMovieHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          movieDetails!.title,
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 8),
        Row(
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.amber,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.star, size: 16, color: Colors.white),
                  SizedBox(width: 4),
                  Text(
                    movieDetails!.voteAverage.toStringAsFixed(1),
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 12),
            Text(
              movieDetails!.releaseDate.isNotEmpty
                  ? movieDetails!.releaseDate.split('-')[0]
                  : 'N/A',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            if (movieDetails!.runtime.isNotEmpty && movieDetails!.runtime != 'N/A') ...[
              SizedBox(width: 12),
              Text(
                '${movieDetails!.runtime} min',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
        SizedBox(height: 8),
        if (movieDetails!.genre.isNotEmpty && movieDetails!.genre != 'N/A')
          Wrap(
            spacing: 8,
            children: movieDetails!.genre.split(', ').map((genre) {
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Text(
                  genre,
                  style: TextStyle(
                    color: Colors.blue[700],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildActionButtons() {
    final trailer = videos.firstWhere(
      (video) => video.type == 'Trailer' && video.site == 'YouTube',
      orElse: () => videos.isNotEmpty ? videos.first : Video(
        iso6391: '', iso31661: '', name: '', key: '', site: '',
        size: 0, type: '', official: false, publishedAt: '', id: '',
      ),
    );

    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: trailer.key.isNotEmpty ? () => _playTrailer(trailer.key) : null,
            icon: Icon(Icons.play_arrow),
            label: Text('Watch Trailer'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        SizedBox(width: 12),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: IconButton(
            onPressed: () {
              // TODO: Add to watchlist
            },
            icon: Icon(Icons.bookmark_border),
            color: Colors.grey[700],
          ),
        ),
        SizedBox(width: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: IconButton(
            onPressed: () {
              // TODO: Share movie
            },
            icon: Icon(Icons.share),
            color: Colors.grey[700],
          ),
        ),
      ],
    );
  }

  Widget _buildOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 8),
        Text(
          movieDetails!.overview.isNotEmpty
              ? movieDetails!.overview
              : 'No overview available.',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[700],
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildMovieInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Movie Info',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 16),
        _buildInfoRow('Release Date', movieDetails!.releaseDate),
        _buildInfoRow('Runtime', movieDetails!.runtime.isNotEmpty ? movieDetails!.runtime : 'N/A'),
        _buildInfoRow('Director', movieDetails!.director.isNotEmpty ? movieDetails!.director : 'N/A'),
        _buildInfoRow('Writer', movieDetails!.writer.isNotEmpty ? movieDetails!.writer : 'N/A'),
        _buildInfoRow('Actors', movieDetails!.actors.isNotEmpty ? movieDetails!.actors : 'N/A'),
        _buildInfoRow('Language', movieDetails!.language.isNotEmpty ? movieDetails!.language : 'N/A'),
        _buildInfoRow('Country', movieDetails!.country.isNotEmpty ? movieDetails!.country : 'N/A'),
        if (movieDetails!.awards.isNotEmpty && movieDetails!.awards != 'N/A')
          _buildInfoRow('Awards', movieDetails!.awards),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }



  Future<void> _playTrailer(String videoKey) async {
    final url = TMDBService.getYouTubeUrl(videoKey);
    final uri = Uri.parse(url);

    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch trailer';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open trailer'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
