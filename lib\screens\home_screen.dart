import 'package:flutter/material.dart';
import '../services/tmdb_service.dart';
import '../widgets/movie_list.dart';
import '../widgets/movie_card.dart';
import '../models/movie.dart';
import '../constants/app_colors.dart';
import '../constants/app_styles.dart';
import 'search_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TMDBService _tmdbService = TMDBService();
  List<Movie> trendingMovies = [];
  bool isLoadingTrending = true;

  @override
  void initState() {
    super.initState();
    _loadTrendingMovies();
  }

  Future<void> _loadTrendingMovies() async {
    try {
      final movies = await _tmdbService.getTrendingMovies();
      setState(() {
        trendingMovies = movies.take(5).toList(); // Take first 5 for carousel
        isLoadingTrending = false;
      });
    } catch (e) {
      setState(() {
        isLoadingTrending = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
        ),
        title: Text(
          '🎬 MovieVerse',
          style: AppStyles.headingLarge.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        centerTitle: true,
        actions: [
          Container(
            margin: EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              gradient: AppColors.neonGradient,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppColors.neonBlue.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: IconButton(
              icon: Icon(
                Icons.search_rounded,
                color: AppColors.textPrimary,
                size: 24,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SearchScreen(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await _loadTrendingMovies();
        },
        child: SingleChildScrollView(
          physics: AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 16),

              // Featured Movies Carousel
              if (isLoadingTrending)
                SizedBox(
                  height: 200,
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                )
              else if (trendingMovies.isNotEmpty)
                FeaturedMovieCarousel(movies: trendingMovies),

              SizedBox(height: 24),

              // Trending Movies
              MovieList(
                title: '🔥 Trending',
                fetchMovies: () => _tmdbService.getTrendingMovies(),
              ),

              SizedBox(height: 24),

              // Now Playing Movies
              MovieList(
                title: '🎭 Now Playing',
                fetchMovies: () => _tmdbService.getNowPlayingMovies(),
              ),

              SizedBox(height: 24),

              // Top Rated Movies
              MovieList(
                title: '🏆 Top Rated',
                fetchMovies: () => _tmdbService.getTopRatedMovies(),
              ),

              SizedBox(height: 24),

              // Upcoming Movies
              MovieList(
                title: '🚀 Upcoming',
                fetchMovies: () => _tmdbService.getUpcomingMovies(),
              ),

              SizedBox(height: 24),

              // Popular Movies
              MovieList(
                title: '⭐ Popular',
                fetchMovies: () => _tmdbService.getPopularMovies(),
              ),

              SizedBox(height: 32),
            ],
          ),
        ),
      ),

    );
  }


}

class FeaturedMovieCarousel extends StatefulWidget {
  final List<Movie> movies;

  const FeaturedMovieCarousel({
    super.key,
    required this.movies,
  });

  @override
  _FeaturedMovieCarouselState createState() => _FeaturedMovieCarouselState();
}

class _FeaturedMovieCarouselState extends State<FeaturedMovieCarousel> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _startAutoScroll();
  }

  void _startAutoScroll() {
    Future.delayed(Duration(seconds: 4), () {
      if (mounted && widget.movies.isNotEmpty) {
        _currentIndex = (_currentIndex + 1) % widget.movies.length;
        _pageController.animateToPage(
          _currentIndex,
          duration: Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
        _startAutoScroll();
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 200,
      child: Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemCount: widget.movies.length,
            itemBuilder: (context, index) {
              return MovieCardLarge(movie: widget.movies[index]);
            },
          ),
          // Page Indicators
          Positioned(
            bottom: 20,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                widget.movies.length,
                (index) => Container(
                  width: 8,
                  height: 8,
                  margin: EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentIndex == index
                        ? Colors.white
                        : Colors.white.withOpacity(0.4),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
