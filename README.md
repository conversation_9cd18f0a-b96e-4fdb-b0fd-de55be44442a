# MovieVerse - Flutter Movie App

A beautiful Flutter mobile application for discovering movies, viewing details, and watching trailers. Built as a graduation project using The Movie Database (TMDB) API.

## Features

### 🏠 Home Screen
- **Trending Movies** - Most popular movies of the day
- **Now Playing** - Currently playing in theaters
- **Top Rated** - Highest rated movies of all time
- **Upcoming** - Movies coming soon to theaters
- **Featured Carousel** - Auto-scrolling showcase of trending movies

### 🎬 Movie Details
- **High-quality poster and backdrop images**
- **Complete movie information** (title, release date, rating, runtime)
- **Detailed overview and synopsis**
- **Genre tags and production details**
- **Cast and crew information with photos**
- **Movie trailers** (YouTube integration)
- **Budget and revenue information**

### 🔍 Search
- **Real-time search** with live suggestions
- **Popular search suggestions**
- **Grid layout** for search results
- **Debounced search** for better performance

## Technical Stack

- **Framework**: Flutter SDK
- **Language**: Dart
- **API**: The Movie Database (TMDB) API
- **State Management**: Provider (ready for implementation)
- **Image Caching**: cached_network_image
- **HTTP Requests**: http package
- **Video Player**: YouTube integration via url_launcher

## Setup Instructions

### Prerequisites
- Flutter SDK (3.0.0 or higher)
- Dart SDK
- Android Studio / VS Code
- TMDB API Key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd task_flutter
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Get TMDB API Key**
   - Visit [TMDB API](https://developer.themoviedb.org/)
   - Create an account and generate an API key
   - Copy your API key

4. **Configure API Key**
   - Open `lib/constants/api_constants.dart`
   - Replace `YOUR_TMDB_API_KEY_HERE` with your actual API key:
   ```dart
   static const String apiKey = 'your_actual_api_key_here';
   ```

5. **Run the app**
   ```bash
   flutter run
   ```

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── constants/
│   └── api_constants.dart    # API configuration
├── models/
│   ├── movie.dart           # Movie data models
│   └── cast.dart            # Cast and crew models
├── services/
│   └── tmdb_service.dart    # API service layer
├── screens/
│   ├── home_screen.dart     # Main home screen
│   ├── movie_details_screen.dart # Movie details view
│   └── search_screen.dart   # Search functionality
└── widgets/
    ├── movie_card.dart      # Reusable movie card
    ├── movie_list.dart      # Horizontal movie lists
    └── cast_list.dart       # Cast display widget
```

## API Endpoints Used

- **Trending Movies**: `/trending/movie/day`
- **Now Playing**: `/movie/now_playing`
- **Top Rated**: `/movie/top_rated`
- **Upcoming**: `/movie/upcoming`
- **Movie Details**: `/movie/{movie_id}`
- **Movie Credits**: `/movie/{movie_id}/credits`
- **Movie Videos**: `/movie/{movie_id}/videos`
- **Search Movies**: `/search/movie`

## Features Implemented

✅ Home screen with movie categories
✅ Movie details with full information
✅ Search functionality with live suggestions
✅ Cast and crew information
✅ Movie trailers (YouTube integration)
✅ Responsive design for different screen sizes
✅ Error handling and loading states
✅ Image caching for better performance
✅ Clean architecture with separation of concerns

## Screenshots

*Add screenshots of your app here*

## Future Enhancements

- [ ] User authentication and profiles
- [ ] Favorites and watchlist functionality
- [ ] Movie recommendations
- [ ] Reviews and ratings
- [ ] Offline support
- [ ] Dark mode theme
- [ ] Social sharing features

## Contributing

This is a graduation project. For any questions or suggestions, please contact the developer.

## License

This project is created for educational purposes as part of a graduation project.

## Acknowledgments

- [The Movie Database (TMDB)](https://www.themoviedb.org/) for providing the movie data API
- Flutter team for the amazing framework
- All the open-source packages used in this project
