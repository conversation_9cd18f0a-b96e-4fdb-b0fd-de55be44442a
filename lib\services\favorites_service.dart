import '../models/movie.dart';

class FavoritesService {
  // قائمة الأفلام المفضلة (يمكن تحسينها لاحقاً باستخدام قاعدة بيانات محلية)
  static final List<Movie> _favoriteMovies = [];

  // إضافة فيلم للمفضلة
  static void addToFavorites(Movie movie) {
    if (!_favoriteMovies.any((m) => m.imdbID == movie.imdbID)) {
      _favoriteMovies.add(movie);
    }
  }

  // حذف فيلم من المفضلة
  static void removeFromFavorites(String imdbID) {
    _favoriteMovies.removeWhere((movie) => movie.imdbID == imdbID);
  }

  // التحقق من وجود فيلم في المفضلة
  static bool isFavorite(String imdbID) {
    return _favoriteMovies.any((movie) => movie.imdbID == imdbID);
  }

  // الحصول على قائمة المفضلة
  static List<Movie> getFavorites() {
    return List.from(_favoriteMovies);
  }

  // مسح جميع المفضلة
  static void clearFavorites() {
    _favoriteMovies.clear();
  }

  // عدد الأفلام المفضلة
  static int get favoritesCount => _favoriteMovies.length;
}
