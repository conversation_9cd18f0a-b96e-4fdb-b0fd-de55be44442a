import 'package:flutter/material.dart';
import 'app_colors.dart';

class AppStyles {
  // Text Styles
  static const TextStyle headingLarge = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
    letterSpacing: 1.2,
  );
  
  static const TextStyle headingMedium = TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    letterSpacing: 0.8,
  );
  
  static const TextStyle headingSmall = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    letterSpacing: 0.5,
  );
  
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
    height: 1.5,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
    height: 1.4,
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
    height: 1.3,
  );
  
  static const TextStyle caption = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    color: AppColors.textSecondary,
    letterSpacing: 0.5,
  );
  
  static const TextStyle neonText = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: AppColors.neonBlue,
    letterSpacing: 1.0,
  );
  
  // Button Styles
  static ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: Colors.transparent,
    foregroundColor: AppColors.textPrimary,
    elevation: 0,
    padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(25),
    ),
  );
  
  static ButtonStyle neonButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: Colors.transparent,
    foregroundColor: AppColors.textPrimary,
    elevation: 0,
    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(20),
      side: BorderSide(color: AppColors.neonBlue, width: 2),
    ),
  );
  
  // Card Decorations
  static BoxDecoration cardDecoration = BoxDecoration(
    gradient: AppColors.cardGradient,
    borderRadius: BorderRadius.circular(20),
    boxShadow: [
      BoxShadow(
        color: AppColors.neonBlue.withValues(alpha: 0.1),
        blurRadius: 20,
        offset: Offset(0, 10),
      ),
    ],
  );
  
  static BoxDecoration neonCardDecoration = BoxDecoration(
    gradient: AppColors.cardGradient,
    borderRadius: BorderRadius.circular(20),
    border: Border.all(
      color: AppColors.neonBlue.withValues(alpha: 0.3),
      width: 1,
    ),
    boxShadow: [
      BoxShadow(
        color: AppColors.neonBlue.withValues(alpha: 0.2),
        blurRadius: 25,
        offset: Offset(0, 10),
      ),
    ],
  );
  
  static BoxDecoration movieCardDecoration = BoxDecoration(
    borderRadius: BorderRadius.circular(16),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withValues(alpha: 0.3),
        blurRadius: 15,
        offset: Offset(0, 8),
      ),
    ],
  );
  
  // Input Decorations
  static InputDecoration searchInputDecoration = InputDecoration(
    hintText: 'Search movies...',
    hintStyle: TextStyle(color: AppColors.textSecondary),
    prefixIcon: Icon(Icons.search, color: AppColors.neonBlue),
    filled: true,
    fillColor: AppColors.backgroundCard,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(25),
      borderSide: BorderSide.none,
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(25),
      borderSide: BorderSide(color: AppColors.neonBlue, width: 2),
    ),
  );
  
  // App Bar Style
  static AppBarTheme appBarTheme = AppBarTheme(
    backgroundColor: Colors.transparent,
    elevation: 0,
    titleTextStyle: headingMedium,
    iconTheme: IconThemeData(color: AppColors.textPrimary),
  );
  
  // Bottom Navigation Style
  static BoxDecoration bottomNavDecoration = BoxDecoration(
    gradient: AppColors.cardGradient,
    borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
    boxShadow: [
      BoxShadow(
        color: AppColors.neonBlue.withValues(alpha: 0.1),
        blurRadius: 20,
        offset: Offset(0, -5),
      ),
    ],
  );
}
