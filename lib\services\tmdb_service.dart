import 'dart:convert';
import 'package:http/http.dart' as http;
import '../constants/api_constants.dart';
import '../models/movie.dart';
import '../models/cast.dart';

class TMDBService {
  static const String _baseUrl = ApiConstants.baseUrl;
  static const String _apiKey = ApiConstants.apiKey;

  // Helper method to build URL with API key
  String _buildUrl(String endpoint, {Map<String, String>? queryParams}) {
    final uri = Uri.parse('$_baseUrl$endpoint');
    final params = <String, String>{
      'api_key': _apiKey,
      ...?queryParams,
    };
    return uri.replace(queryParameters: params).toString();
  }

  // Helper method to make HTTP requests
  Future<Map<String, dynamic>> _makeRequest(String url) async {
    try {
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to load data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Get trending movies
  Future<List<Movie>> getTrendingMovies({int page = 1}) async {
    final url = _buildUrl(ApiConstants.trendingMovies, queryParams: {
      'page': page.toString(),
    });

    final data = await _makeRequest(url);
    final List<dynamic> results = data['results'] ?? [];

    return results.map((json) => Movie.fromJson(json)).toList();
  }

  // Get now playing movies
  Future<List<Movie>> getNowPlayingMovies({int page = 1}) async {
    final url = _buildUrl(ApiConstants.nowPlayingMovies, queryParams: {
      'page': page.toString(),
    });

    final data = await _makeRequest(url);
    final List<dynamic> results = data['results'] ?? [];

    return results.map((json) => Movie.fromJson(json)).toList();
  }

  // Get top rated movies
  Future<List<Movie>> getTopRatedMovies({int page = 1}) async {
    final url = _buildUrl(ApiConstants.topRatedMovies, queryParams: {
      'page': page.toString(),
    });

    final data = await _makeRequest(url);
    final List<dynamic> results = data['results'] ?? [];

    return results.map((json) => Movie.fromJson(json)).toList();
  }

  // Get upcoming movies
  Future<List<Movie>> getUpcomingMovies({int page = 1}) async {
    final url = _buildUrl(ApiConstants.upcomingMovies, queryParams: {
      'page': page.toString(),
    });

    final data = await _makeRequest(url);
    final List<dynamic> results = data['results'] ?? [];

    return results.map((json) => Movie.fromJson(json)).toList();
  }

  // Get popular movies
  Future<List<Movie>> getPopularMovies({int page = 1}) async {
    final url = _buildUrl(ApiConstants.popularMovies, queryParams: {
      'page': page.toString(),
      'language': 'en-US',
    });

    final data = await _makeRequest(url);
    final List<dynamic> results = data['results'] ?? [];

    return results.map((json) => Movie.fromJson(json)).toList();
  }

  // Search movies
  Future<List<Movie>> searchMovies(String query, {int page = 1}) async {
    if (query.isEmpty) return [];

    final url = _buildUrl(ApiConstants.searchMovies, queryParams: {
      'query': query,
      'page': page.toString(),
    });

    final data = await _makeRequest(url);
    final List<dynamic> results = data['results'] ?? [];

    return results.map((json) => Movie.fromJson(json)).toList();
  }

  // Get movie details
  Future<MovieDetails> getMovieDetails(int movieId) async {
    final url = _buildUrl('${ApiConstants.movieDetails}/$movieId');

    final data = await _makeRequest(url);
    return MovieDetails.fromJson(data);
  }

  // Get movie credits (cast and crew)
  Future<Credits> getMovieCredits(int movieId) async {
    final url = _buildUrl('${ApiConstants.movieDetails}/$movieId/credits');

    final data = await _makeRequest(url);
    return Credits.fromJson(data);
  }

  // Get movie videos (trailers)
  Future<List<Video>> getMovieVideos(int movieId) async {
    final url = _buildUrl('${ApiConstants.movieDetails}/$movieId/videos');

    final data = await _makeRequest(url);
    final List<dynamic> results = data['results'] ?? [];

    return results.map((json) => Video.fromJson(json)).toList();
  }

  // Get genres list
  Future<List<Genre>> getGenres() async {
    final url = _buildUrl(ApiConstants.genresList);

    final data = await _makeRequest(url);
    final List<dynamic> genres = data['genres'] ?? [];

    return genres.map((json) => Genre.fromJson(json)).toList();
  }

  // Helper method to get full image URL
  static String getImageUrl(String? imagePath, {bool isBackdrop = false}) {
    if (imagePath == null || imagePath.isEmpty) {
      return 'https://via.placeholder.com/500x750?text=No+Image';
    }

    final baseUrl = isBackdrop ? ApiConstants.backdropBaseUrl : ApiConstants.imageBaseUrl;
    return '$baseUrl$imagePath';
  }

  // Helper method to get YouTube trailer URL
  static String getYouTubeUrl(String videoKey) {
    return '${ApiConstants.youtubeBaseUrl}$videoKey';
  }
}
