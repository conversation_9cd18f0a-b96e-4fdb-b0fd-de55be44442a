import 'dart:convert';
import 'package:http/http.dart' as http;
import '../constants/api_constants.dart';
import '../models/movie.dart';
import '../models/cast.dart';

class TMDBService {
  static const String _baseUrl = ApiConstants.baseUrl;
  static const String _apiKey = ApiConstants.apiKey;

  // Helper method to build URL with API key for OMDB
  String _buildUrl({Map<String, String>? queryParams}) {
    final params = <String, String>{
      'apikey': _apiKey,
      ...?queryParams,
    };
    final uri = Uri.parse(_baseUrl);
    return uri.replace(queryParameters: params).toString();
  }

  // Helper method to make HTTP requests
  Future<Map<String, dynamic>> _makeRequest(String url) async {
    try {
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['Response'] == 'False') {
          throw Exception(data['Error'] ?? 'Movie not found');
        }
        return data;
      } else {
        throw Exception('Failed to load data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Search movies using OMDB API
  Future<List<Movie>> searchMovies(String query, {int page = 1}) async {
    if (query.isEmpty) return [];

    final url = _buildUrl(queryParams: {
      's': query,
      'type': 'movie',
      'page': page.toString(),
    });

    final data = await _makeRequest(url);
    final List<dynamic> results = data['Search'] ?? [];

    // For search results, we need to get full details for each movie
    List<Movie> movies = [];
    for (var result in results.take(10)) { // Limit to 10 for performance
      try {
        final movieDetails = await getMovieDetails(result['imdbID']);
        movies.add(movieDetails);
      } catch (e) {
        // Skip movies that can't be loaded
        continue;
      }
    }

    return movies;
  }

  // Get movie details by IMDB ID
  Future<MovieDetails> getMovieDetails(String imdbId) async {
    final url = _buildUrl(queryParams: {
      'i': imdbId,
      'plot': 'full',
    });

    final data = await _makeRequest(url);
    return MovieDetails.fromJson(data);
  }

  // For OMDB API, we'll simulate different categories with popular movie searches
  Future<List<Movie>> getTrendingMovies({int page = 1}) async {
    return await _getMoviesBySearch('Marvel', page: page);
  }

  Future<List<Movie>> getNowPlayingMovies({int page = 1}) async {
    return await _getMoviesBySearch('2024', page: page);
  }

  Future<List<Movie>> getTopRatedMovies({int page = 1}) async {
    return await _getMoviesBySearch('Batman', page: page);
  }

  Future<List<Movie>> getUpcomingMovies({int page = 1}) async {
    return await _getMoviesBySearch('Spider', page: page);
  }

  Future<List<Movie>> getPopularMovies({int page = 1}) async {
    return await _getMoviesBySearch('Star Wars', page: page);
  }

  // Helper method to get movies by search term
  Future<List<Movie>> _getMoviesBySearch(String searchTerm, {int page = 1}) async {
    final url = _buildUrl(queryParams: {
      's': searchTerm,
      'type': 'movie',
      'page': page.toString(),
    });

    try {
      final data = await _makeRequest(url);
      final List<dynamic> results = data['Search'] ?? [];

      // Get full details for first 5 movies
      List<Movie> movies = [];
      for (var result in results.take(5)) {
        try {
          final movieDetails = await getMovieDetails(result['imdbID']);
          movies.add(movieDetails);
        } catch (e) {
          continue;
        }
      }

      return movies;
    } catch (e) {
      return [];
    }
  }

  // OMDB doesn't have cast/crew info, so return empty
  Future<Credits> getMovieCredits(String movieId) async {
    return Credits(id: 0, cast: [], crew: []);
  }

  // OMDB doesn't have video info, so return empty
  Future<List<Video>> getMovieVideos(String movieId) async {
    return [];
  }

  // OMDB doesn't have genres list, so return empty
  Future<List<Genre>> getGenres() async {
    return [];
  }

  // Helper method to get full image URL for OMDB
  static String getImageUrl(String? imagePath, {bool isBackdrop = false}) {
    if (imagePath == null || imagePath.isEmpty || imagePath == 'N/A') {
      return ApiConstants.defaultPosterUrl;
    }

    // OMDB returns full URLs, so return as is
    return imagePath;
  }

  // Helper method to get YouTube trailer URL
  static String getYouTubeUrl(String videoKey) {
    return '${ApiConstants.youtubeBaseUrl}$videoKey';
  }
}
