import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'screens/main_screen.dart';
import 'constants/app_colors.dart';
import 'constants/app_styles.dart';

void main() {
  runApp(MovieVerseApp());
}

class MovieVerseApp extends StatelessWidget {
  const MovieVerseApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MovieVerse',
      theme: ThemeData(
        brightness: Brightness.dark,
        primaryColor: AppColors.neonBlue,
        scaffoldBackgroundColor: AppColors.backgroundPrimary,
        fontFamily: 'SF Pro Text',
        visualDensity: VisualDensity.adaptivePlatformDensity,
        appBarTheme: AppStyles.appBarTheme,
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: AppStyles.primaryButtonStyle,
        ),
        textTheme: TextTheme(
          headlineLarge: AppStyles.headingLarge,
          headlineMedium: AppStyles.headingMedium,
          headlineSmall: AppStyles.headingSmall,
          bodyLarge: AppStyles.bodyLarge,
          bodyMedium: AppStyles.bodyMedium,
          bodySmall: AppStyles.bodySmall,
        ),
        colorScheme: ColorScheme.dark(
          primary: AppColors.neonBlue,
          secondary: AppColors.neonPurple,
          surface: AppColors.backgroundCard,
        ),
      ),
      home: MainScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

