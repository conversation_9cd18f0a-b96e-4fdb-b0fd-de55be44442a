import 'package:flutter/material.dart';
import '../services/favorites_service.dart';
import 'favorites_screen.dart';

class ProfileScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Profile',
          style: TextStyle(
            color: Colors.black87,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(height: 20),
            // Profile Avatar
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: [Colors.blue[400]!, Colors.purple[400]!],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: Offset(0, 10),
                  ),
                ],
              ),
              child: Icon(
                Icons.person,
                size: 60,
                color: Colors.white,
              ),
            ),
            SizedBox(height: 20),

            // User Name
            Text(
              'Movie Lover',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Exploring the world of cinema',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 30),

            // Stats Cards
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Favorites',
                    '${FavoritesService.getFavorites().length}',
                    Icons.favorite,
                    Colors.red,
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'Watched',
                    '${FavoritesService.getFavorites().length * 2}',
                    Icons.visibility,
                    Colors.green,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Reviews',
                    '${FavoritesService.getFavorites().length}',
                    Icons.rate_review,
                    Colors.orange,
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'Watchlist',
                    '${(FavoritesService.getFavorites().length * 1.5).round()}',
                    Icons.bookmark,
                    Colors.blue,
                  ),
                ),
              ],
            ),

            SizedBox(height: 40),

            // Menu Items
            _buildMenuItem(
              Icons.favorite,
              'My Favorites',
              'View your favorite movies',
              Colors.red,
              () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => FavoritesScreen()),
                );
              },
            ),
            _buildMenuItem(
              Icons.settings,
              'Settings',
              'App preferences and settings',
              Colors.grey[700]!,
              () {
                _showSettingsDialog(context);
              },
            ),
            _buildMenuItem(
              Icons.info_outline,
              'About',
              'About MovieVerse app',
              Colors.blue,
              () {
                _showAboutDialog(context);
              },
            ),
            _buildMenuItem(
              Icons.share,
              'Share App',
              'Share MovieVerse with friends',
              Colors.green,
              () {
                _shareApp(context);
              },
            ),
            _buildMenuItem(
              Icons.star_rate,
              'Rate App',
              'Rate us on the app store',
              Colors.amber,
              () {
                _rateApp(context);
              },
            ),

            SizedBox(height: 40),

            // App Version
            Text(
              'MovieVerse v1.0.0',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
            ),
            Text(
              'Made with ❤️ for movie lovers',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 30,
            color: color,
          ),
          SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(
    IconData icon,
    String title,
    String subtitle,
    Color iconColor,
    VoidCallback onTap,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: iconColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: iconColor,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey[400],
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Settings'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(Icons.notifications),
                title: Text('Notifications'),
                trailing: Switch(
                  value: true,
                  onChanged: (value) {},
                ),
              ),
              ListTile(
                leading: Icon(Icons.dark_mode),
                title: Text('Dark Mode'),
                trailing: Switch(
                  value: false,
                  onChanged: (value) {},
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Close'),
            ),
          ],
        );
      },
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'MovieVerse',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue[400]!, Colors.purple[400]!],
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          Icons.movie,
          color: Colors.white,
          size: 30,
        ),
      ),
      children: [
        Text(
          'MovieVerse is your ultimate movie discovery companion. '
          'Explore thousands of movies, create your favorites list, '
          'and never miss a great film again!',
        ),
        SizedBox(height: 16),
        Text(
          'Powered by OMDB API',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  void _shareApp(BuildContext context) {
    // في التطبيق الحقيقي، يمكن استخدام share_plus package
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('🎬 Check out MovieVerse - the best movie discovery app!'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 3),
        action: SnackBarAction(
          label: 'Copy Link',
          textColor: Colors.white,
          onPressed: () {
            // نسخ رابط التطبيق
          },
        ),
      ),
    );
  }

  void _rateApp(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Rate MovieVerse'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('How would you rate your experience?'),
              SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  return IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Thank you for rating us ${index + 1} stars! ⭐'),
                          backgroundColor: Colors.amber,
                        ),
                      );
                    },
                    icon: Icon(
                      Icons.star,
                      color: Colors.amber,
                      size: 30,
                    ),
                  );
                }),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Maybe Later'),
            ),
          ],
        );
      },
    );
  }
}
