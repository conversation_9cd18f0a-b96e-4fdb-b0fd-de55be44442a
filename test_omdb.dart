import 'dart:convert';\nimport 'package:http/http.dart' as http;\n\nvoid main() async {\n  // Test OMDB API with your key\n  final apiKey = '89bcecaf';\n  final testUrl = 'http://www.omdbapi.com/?i=tt3896198&apikey=$apiKey';\n  \n  try {\n    final response = await http.get(Uri.parse(testUrl));\n    \n    if (response.statusCode == 200) {\n      final data = json.decode(response.body);\n      print('✅ OMDB API Test Successful!');\n      print('Movie Title: ${data['Title']}');\n      print('Year: ${data['Year']}');\n      print('Director: ${data['Director']}');\n      print('Plot: ${data['Plot']}');\n    } else {\n      print('❌ HTTP Error: ${response.statusCode}');\n    }\n  } catch (e) {\n    print('❌ Network Error: $e');\n  }\n}
